/**
 * 项目KPI快速录入组件
 * 基于AreaMetricQuickInput设计，适配项目KPI数据结构
 */

import { useState, useEffect } from 'react'
import { Card, CardContent } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Textarea } from '../ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '../ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu'
import { 
  Plus, 
  Edit, 
  Trash2, 
  MoreHorizontal, 
  TrendingUp, 
  TrendingDown, 
  Target,
  History,
  ChevronDown,
  ChevronUp
} from 'lucide-react'
import { cn } from '../../lib/utils'
import { useUIStore } from '../../store/uiStore'
import { useLanguage } from '../../contexts/LanguageContext'
// {{ AURA-X: Add - 导入双向KPI计算函数. Approval: 寸止(ID:1738157400). }}
import { calculateKPIProgress } from '../../lib/kpiProgressCalculator'
import { databaseApi } from '../../lib/api'
import type { ProjectKPI, KPIRecord } from '../../../../shared/types'

interface ProjectKPIQuickInputProps {
  kpi: ProjectKPI
  onRecordCreated?: (record: KPIRecord) => void
  onEdit?: (kpi: ProjectKPI) => void
  onDelete?: (kpi: ProjectKPI) => void
  className?: string
}

export function ProjectKPIQuickInput({ 
  kpi, 
  onRecordCreated, 
  onEdit, 
  onDelete, 
  className 
}: ProjectKPIQuickInputProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [value, setValue] = useState('')
  const [note, setNote] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)
  const [historyRecords, setHistoryRecords] = useState<KPIRecord[]>([])
  const [loadingHistory, setLoadingHistory] = useState(false)
  const { addNotification } = useUIStore()
  const { t } = useLanguage()

  // {{ AURA-X: Add - 计算KPI进度和状态. Approval: 寸止(ID:1738157400). }}
  const progress = kpi.target ? calculateKPIProgress({
    id: kpi.id,
    name: kpi.name,
    value: parseFloat(kpi.value) || 0,
    target: parseFloat(kpi.target) || 0,
    unit: kpi.unit,
    direction: (kpi as any).direction || 'increase',
    frequency: kpi.frequency,
    updatedAt: kpi.updatedAt
  }) : 0

  const getProgressColor = (progress: number) => {
    if (progress >= 100) return 'text-green-600'
    if (progress >= 75) return 'text-blue-600'
    if (progress >= 50) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getProgressBadgeVariant = (progress: number) => {
    if (progress >= 100) return 'default'
    if (progress >= 75) return 'secondary'
    if (progress >= 50) return 'outline'
    return 'destructive'
  }

  // 加载历史记录
  const loadHistory = async () => {
    if (loadingHistory) return
    
    setLoadingHistory(true)
    try {
      const result = await databaseApi.getKPIRecords(kpi.id)
      if (result.success) {
        setHistoryRecords(result.data || [])
      }
    } catch (error) {
      console.error('Failed to load KPI history:', error)
    } finally {
      setLoadingHistory(false)
    }
  }

  // 展开时加载历史记录
  useEffect(() => {
    if (isExpanded && historyRecords.length === 0) {
      loadHistory()
    }
  }, [isExpanded])

  const handleSubmit = async () => {
    if (!value.trim()) return

    setIsSubmitting(true)
    try {
      const result = await databaseApi.createKPIRecord({
        kpiId: kpi.id,
        value: value.trim(),
        note: note.trim() || undefined
      })

      if (result.success) {
        addNotification({
          type: 'success',
          title: t('common.success'),
          message: `${kpi.name} updated successfully`
        })
        
        setValue('')
        setNote('')
        setIsOpen(false)
        
        // 刷新历史记录
        if (isExpanded) {
          loadHistory()
        }
        
        onRecordCreated?.(result.data)
      } else {
        throw new Error(result.error || 'Failed to create record')
      }
    } catch (error) {
      console.error('Failed to create KPI record:', error)
      addNotification({
        type: 'error',
        title: t('common.error'),
        message: 'Failed to update KPI'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className={cn("group hover:shadow-md transition-shadow", className)}>
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* 主要信息行 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3 flex-1 min-w-0">
              {/* KPI名称和方向图标 */}
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-1">
                  {(kpi as any).direction === 'decrease' ? (
                    <TrendingDown className="h-4 w-4 text-orange-500" />
                  ) : (
                    <TrendingUp className="h-4 w-4 text-green-500" />
                  )}
                  <h4 className="font-medium truncate">{kpi.name}</h4>
                </div>
              </div>

              {/* 当前值和目标 */}
              <div className="flex items-center gap-2">
                <span className="text-lg font-semibold">
                  {kpi.value}{kpi.unit && ` ${kpi.unit}`}
                </span>
                {kpi.target && (
                  <>
                    <span className="text-muted-foreground">/</span>
                    <span className="text-muted-foreground">
                      {kpi.target}{kpi.unit && ` ${kpi.unit}`}
                    </span>
                  </>
                )}
              </div>

              {/* 进度徽章 */}
              {kpi.target && (
                <Badge variant={getProgressBadgeVariant(progress)} className="ml-auto">
                  {Math.round(progress)}%
                </Badge>
              )}
            </div>

            {/* 操作按钮区域 */}
            <div className="flex items-center gap-1" onClick={(e) => e.stopPropagation()}>
              <Dialog open={isOpen} onOpenChange={setIsOpen}>
                <DialogTrigger asChild>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Update {kpi.name}</DialogTitle>
                    <DialogDescription>
                      Record a new value for this KPI
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="value">New Value</Label>
                      <Input
                        id="value"
                        type="number"
                        value={value}
                        onChange={(e) => setValue(e.target.value)}
                        placeholder={`Current: ${kpi.value}`}
                      />
                    </div>
                    <div>
                      <Label htmlFor="note">Note (optional)</Label>
                      <Textarea
                        id="note"
                        value={note}
                        onChange={(e) => setNote(e.target.value)}
                        placeholder="Add a note about this update..."
                        rows={3}
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button
                      variant="outline"
                      onClick={() => setIsOpen(false)}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleSubmit}
                      disabled={isSubmitting || !value.trim()}
                    >
                      {isSubmitting ? 'Recording...' : 'Record'}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => onEdit?.(kpi)}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit KPI
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => setIsExpanded(!isExpanded)}
                    className="flex items-center"
                  >
                    <History className="h-4 w-4 mr-2" />
                    {isExpanded ? 'Hide History' : 'Show History'}
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => onDelete?.(kpi)}
                    className="text-destructive"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* 展开/折叠按钮 */}
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setIsExpanded(!isExpanded)}
                className="opacity-0 group-hover:opacity-100 transition-opacity"
              >
                {isExpanded ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          {/* 进度条 */}
          {kpi.target && (
            <div className="space-y-1">
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>Progress</span>
                <span className={getProgressColor(progress)}>
                  {Math.round(progress)}%
                </span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div
                  className={cn(
                    "h-2 rounded-full transition-all duration-300",
                    progress >= 100 ? "bg-green-500" :
                    progress >= 75 ? "bg-blue-500" :
                    progress >= 50 ? "bg-yellow-500" : "bg-red-500"
                  )}
                  style={{ width: `${Math.min(progress, 100)}%` }}
                />
              </div>
            </div>
          )}

          {/* 历史记录展开区域 */}
          {isExpanded && (
            <div className="border-t pt-3 space-y-2">
              <div className="flex items-center justify-between">
                <h5 className="text-sm font-medium text-muted-foreground">Recent History</h5>
                {loadingHistory && (
                  <div className="text-xs text-muted-foreground">Loading...</div>
                )}
              </div>

              {historyRecords.length === 0 ? (
                <div className="text-xs text-muted-foreground text-center py-2">
                  No history records yet
                </div>
              ) : (
                <div className="space-y-1 max-h-32 overflow-y-auto">
                  {historyRecords.slice(0, 5).map((record) => (
                    <div key={record.id} className="flex items-center justify-between text-xs">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">
                          {record.value}{kpi.unit && ` ${kpi.unit}`}
                        </span>
                        {record.note && (
                          <span className="text-muted-foreground truncate max-w-32">
                            {record.note}
                          </span>
                        )}
                      </div>
                      <span className="text-muted-foreground">
                        {new Date(record.recordedAt).toLocaleDateString()}
                      </span>
                    </div>
                  ))}
                  {historyRecords.length > 5 && (
                    <div className="text-xs text-muted-foreground text-center">
                      +{historyRecords.length - 5} more records
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export default ProjectKPIQuickInput
